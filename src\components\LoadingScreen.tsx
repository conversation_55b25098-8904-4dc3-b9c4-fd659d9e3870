import { useEffect, useState, useCallback, useMemo } from "react";
import { useSpring, animated } from "@react-spring/web";

interface LoadingScreenProps {
  onComplete: () => void;
  isVisible: boolean;
  skipDelay?: number;
  maxDuration?: number;
}

const LoadingScreen = ({
  onComplete,
  isVisible,
  skipDelay = 1000,
  maxDuration = 10000,
}: LoadingScreenProps) => {
  const [animationStage, setAnimationStage] = useState<
    "pen" | "writing" | "complete"
  >("pen");
  const [showText, setShowText] = useState(false);
  const [canSkip, setCanSkip] = useState(false);
  const [isSkipped, setIsSkipped] = useState(false);

  // Memoize reduced motion check for performance
  const prefersReducedMotion = useMemo(
    () =>
      typeof window !== "undefined" &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches,
    []
  );

  // Main container animation
  const containerSpring = useSpring({
    opacity: isVisible ? 1 : 0,
    transform: isVisible ? "scale(1)" : "scale(0.95)",
    config: { tension: 200, friction: 25 },
  });

  // Feather pen animation
  const penSpring = useSpring({
    transform:
      animationStage === "pen"
        ? "translateX(-100px) translateY(20px) rotate(-15deg)"
        : "translateX(0px) translateY(0px) rotate(0deg)",
    opacity: animationStage === "complete" ? 0 : 1,
    config: { tension: 120, friction: 14 },
  });

  // Text reveal animation
  const textSpring = useSpring({
    opacity: showText ? 1 : 0,
    transform: showText ? "translateY(0px)" : "translateY(20px)",
    config: { tension: 150, friction: 20 },
  });

  // Skip button animation
  const skipSpring = useSpring({
    opacity: canSkip ? 1 : 0,
    transform: canSkip ? "translateY(0px)" : "translateY(10px)",
    config: { tension: 200, friction: 25 },
  });

  // Handle skip functionality with accessibility announcements
  const handleSkip = useCallback(() => {
    if (isSkipped) return; // Prevent multiple skips

    setIsSkipped(true);
    setAnimationStage("complete");

    // Announce to screen readers
    const announcement = document.createElement("div");
    announcement.setAttribute("aria-live", "polite");
    announcement.setAttribute("aria-atomic", "true");
    announcement.className = "sr-only";
    announcement.textContent =
      "Loading animation skipped. Proceeding to main content.";
    document.body.appendChild(announcement);

    setTimeout(() => {
      onComplete();
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 300);
  }, [onComplete, isSkipped]);

  // Handle key press for skip
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (
        (e.key === "Enter" || e.key === " " || e.key === "Escape") &&
        canSkip
      ) {
        e.preventDefault();
        handleSkip();
      }
    };

    if (canSkip) {
      window.addEventListener("keydown", handleKeyPress);
      return () => window.removeEventListener("keydown", handleKeyPress);
    }
  }, [canSkip, handleSkip]);

  // Enhanced animation sequence with safety features
  useEffect(() => {
    if (!isVisible || isSkipped) return;

    const timeouts: NodeJS.Timeout[] = [];

    const addTimeout = (callback: () => void, delay: number) => {
      const timeout = setTimeout(callback, delay);
      timeouts.push(timeout);
      return timeout;
    };

    const sequence = () => {
      // Allow skipping after specified delay
      addTimeout(() => setCanSkip(true), skipDelay);

      if (prefersReducedMotion) {
        // Reduced motion: quick sequence
        addTimeout(() => setShowText(true), 200);
        addTimeout(() => setAnimationStage("complete"), 600);
        addTimeout(onComplete, 800);
        return;
      }

      // Normal animation sequence with performance optimization
      // Stage 1: Pen enters (already started)
      addTimeout(() => setAnimationStage("writing"), 800);

      // Stage 2: Text appears as pen "writes"
      addTimeout(() => setShowText(true), 1200);

      // Stage 3: Complete animation
      addTimeout(
        () => setAnimationStage("complete"),
        Math.min(2800, maxDuration - 400)
      );
      addTimeout(onComplete, Math.min(3200, maxDuration));
    };

    sequence();

    // Cleanup function to prevent memory leaks
    return () => {
      timeouts.forEach((timeout) => clearTimeout(timeout));
    };
  }, [
    isVisible,
    onComplete,
    prefersReducedMotion,
    isSkipped,
    skipDelay,
    maxDuration,
  ]);

  if (!isVisible) return null;

  return (
    <animated.div
      style={containerSpring}
      className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-brand-primary to-brand-neutral/30"
      onClick={canSkip ? handleSkip : undefined}
      role="dialog"
      aria-label="Loading screen"
      aria-live="polite"
    >
      <div className="relative flex flex-col items-center justify-center max-w-md mx-auto px-8">
        {/* Feather Pen SVG */}
        <animated.div
          style={penSpring}
          className="relative mb-8"
          aria-hidden="true"
        >
          <svg
            width="120"
            height="120"
            viewBox="0 0 120 120"
            className="text-brand-accent drop-shadow-lg"
          >
            {/* Feather */}
            <path
              d="M20 100 L60 20 L65 22 L70 25 L75 30 L78 35 L80 40 L82 45 L83 50 L84 55 L85 60 L86 65 L87 70 L88 75 L89 80 L90 85 L91 90 L92 95 L25 102 Z"
              fill="currentColor"
              opacity="0.8"
            />
            {/* Pen tip */}
            <path d="M20 100 L25 102 L22 105 Z" fill="currentColor" />
            {/* Feather details */}
            <path
              d="M60 20 L65 25 M65 30 L70 35 M70 40 L75 45 M75 50 L80 55"
              stroke="currentColor"
              strokeWidth="1"
              opacity="0.6"
              fill="none"
            />
          </svg>

          {/* Ink trail effect */}
          {animationStage === "writing" && (
            <div className="absolute -bottom-2 -right-2 w-3 h-3 bg-brand-accent rounded-full animate-pulse" />
          )}
        </animated.div>

        {/* Writing text */}
        <animated.div style={textSpring} className="text-center">
          <h1 className="text-2xl md:text-3xl font-serif font-bold text-brand-secondary mb-2">
            The Brown Patience Company
          </h1>
          <p className="text-brand-secondary/70 text-sm md:text-base">
            Crafting stories, one word at a time...
          </p>
        </animated.div>

        {/* Skip button */}
        <animated.div
          style={skipSpring}
          className="absolute bottom-8 left-1/2 mt- transform -translate-x-1/2"
        >
          {canSkip && (
            <button
              onClick={handleSkip}
              className="text-brand-secondary/60 hover:text-brand-accent text-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-brand-accent focus:ring-opacity-50 rounded px-3 py-1"
              aria-label="Skip loading animation"
            >
              Click to continue →
            </button>
          )}
        </animated.div>
      </div>
    </animated.div>
  );
};

export default LoadingScreen;
