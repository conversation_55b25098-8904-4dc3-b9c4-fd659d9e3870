import { useLocation } from "react-router-dom";
import { useTransition, animated, config } from "@react-spring/web";
import { ReactNode, useEffect, useState, useCallback, useMemo } from "react";

interface PageTransitionProps {
  children: ReactNode;
}

const PageTransition = ({ children }: PageTransitionProps) => {
  const location = useLocation();
  const [displayLocation, setDisplayLocation] = useState(location);
  const [transitionStage, setTransitionStage] = useState("fadeIn");
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Memoize reduced motion check for performance
  const prefersReducedMotion = useMemo(
    () =>
      typeof window !== "undefined" &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches,
    []
  );

  // Get page title for accessibility
  const getPageTitle = useCallback((pathname: string) => {
    const routes: Record<string, string> = {
      "/": "Home",
      "/about": "About",
      "/books": "Books",
      "/blog": "Blog",
      "/community": "Community",
      "/contact": "Contact",
      "/services/book-writing-editing": "Book Writing and Editing Services",
      "/services/coaching-for-writers": "Coaching for Writers",
      "/services/content-writing": "Content Writing Services",
      "/subscriptions/guidance-for-solopreneurs": "Guidance for Solopreneurs",
      "/subscriptions/coaching-for-authors": "Coaching for Authors",
    };
    return routes[pathname] || "Page";
  }, []);

  // Update display location when location changes
  useEffect(() => {
    if (location !== displayLocation) {
      setIsTransitioning(true);
      setTransitionStage("fadeOut");
    }
  }, [location, displayLocation]);

  // Handle transition stages
  useEffect(() => {
    if (transitionStage === "fadeOut") {
      const timer = setTimeout(
        () => {
          setDisplayLocation(location);
          setTransitionStage("fadeIn");
          setIsTransitioning(false);
        },
        prefersReducedMotion ? 50 : 400
      ); // Faster transition for reduced motion

      return () => clearTimeout(timer);
    }
  }, [transitionStage, location, prefersReducedMotion]);

  // Announce page changes to screen readers
  useEffect(() => {
    const pageTitle = getPageTitle(displayLocation.pathname);
    const announcement = `Navigated to ${pageTitle} page`;

    // Create a live region announcement
    const announcer = document.createElement("div");
    announcer.setAttribute("aria-live", "polite");
    announcer.setAttribute("aria-atomic", "true");
    announcer.className = "sr-only";
    announcer.textContent = announcement;

    document.body.appendChild(announcer);

    // Clean up after announcement
    setTimeout(() => {
      if (document.body.contains(announcer)) {
        document.body.removeChild(announcer);
      }
    }, 1000);
  }, [displayLocation.pathname, getPageTitle]);

  // Optimized transition configuration
  const transitionConfig = useMemo(
    () => ({
      from: {
        opacity: 0,
        transform: prefersReducedMotion
          ? "translateX(0px)"
          : "perspective(1200px) rotateY(90deg) translateZ(-100px)",
      },
      enter: {
        opacity: 1,
        transform: prefersReducedMotion
          ? "translateX(0px)"
          : "perspective(1200px) rotateY(0deg) translateZ(0px)",
      },
      leave: {
        opacity: 0,
        transform: prefersReducedMotion
          ? "translateX(0px)"
          : "perspective(1200px) rotateY(-90deg) translateZ(-100px)",
      },
      config: prefersReducedMotion
        ? { ...config.default, duration: 100 }
        : {
            tension: 120,
            friction: 14,
            mass: 0.8,
          },
      keys: (location: typeof displayLocation) => location.pathname,
      // Performance optimizations
      immediate: false,
      trail: 0,
      expires: true,
    }),
    [prefersReducedMotion]
  );

  // Create transitions with book flip effect
  const transitions = useTransition(displayLocation, transitionConfig);

  return (
    <div
      className="page-transition-wrapper"
      role="region"
      aria-label="Page content area"
    >
      {transitions((style, item) => {
        const pageTitle = getPageTitle(item.pathname);
        return (
          <animated.div
            key={item.pathname}
            style={style}
            className="page-transition-container"
            role="main"
            aria-live="polite"
            aria-label={`${pageTitle} page content`}
            aria-busy={isTransitioning}
            data-page={item.pathname}
          >
            <div className="page-content">{children}</div>
          </animated.div>
        );
      })}
    </div>
  );
};

export default PageTransition;
