import { useLocation } from "react-router-dom";
import { useSpring, animated } from "@react-spring/web";
import { ReactNode, useEffect, useMemo, useState } from "react";

interface PageTransitionProps {
  children: ReactNode;
}

const PageTransition = ({ children }: PageTransitionProps) => {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(true);

  // Check for reduced motion preference
  const prefersReducedMotion = useMemo(
    () =>
      typeof window !== "undefined" &&
      window.matchMedia("(prefers-reduced-motion: reduce)").matches,
    []
  );

  // Create spring animation for page transitions
  const springProps = useSpring({
    opacity: isVisible ? 1 : 0,
    transform: prefersReducedMotion
      ? `translateX(${isVisible ? 0 : 20}px)`
      : `perspective(1200px) rotateY(${isVisible ? 0 : 15}deg) translateZ(${
          isVisible ? 0 : -50
        }px)`,
    config: prefersReducedMotion
      ? { duration: 150 }
      : {
          tension: 120,
          friction: 14,
          mass: 0.8,
        },
  });

  // Handle route changes
  useEffect(() => {
    setIsVisible(false);
    const timer = setTimeout(
      () => {
        setIsVisible(true);
      },
      prefersReducedMotion ? 50 : 200
    );

    return () => clearTimeout(timer);
  }, [location.pathname, prefersReducedMotion]);

  return (
    <animated.div
      style={springProps}
      className="page-transition-container"
      role="main"
      aria-label="Page content"
    >
      <div className="page-content">{children}</div>
    </animated.div>
  );
};

export default PageTransition;
